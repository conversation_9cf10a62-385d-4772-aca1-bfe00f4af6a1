imap_tools-1.11.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
imap_tools-1.11.0.dist-info/METADATA,sha256=682Uomx-rq8ivU80FkIi-iyQIUykYdf3Gk_yI7_S7R8,22627
imap_tools-1.11.0.dist-info/RECORD,,
imap_tools-1.11.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imap_tools-1.11.0.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
imap_tools-1.11.0.dist-info/licenses/LICENSE,sha256=8PLDrrWnFVEiHHyGO2XYPUvP7dVsAGb2G615z1kRcck,11548
imap_tools-1.11.0.dist-info/top_level.txt,sha256=AS9xElvqoWkjbjpoTUbPOjyd7eV5Jv7KxKspzqz54AE,11
imap_tools/__init__.py,sha256=SC-hGYAOtq9tKZzWe8ahjmDFXqnhOFggiVEE_HPfNC8,596
imap_tools/__pycache__/__init__.cpython-312.pyc,,
imap_tools/__pycache__/consts.cpython-312.pyc,,
imap_tools/__pycache__/errors.cpython-312.pyc,,
imap_tools/__pycache__/folder.cpython-312.pyc,,
imap_tools/__pycache__/idle.cpython-312.pyc,,
imap_tools/__pycache__/imap_utf7.cpython-312.pyc,,
imap_tools/__pycache__/mailbox.cpython-312.pyc,,
imap_tools/__pycache__/message.cpython-312.pyc,,
imap_tools/__pycache__/query.cpython-312.pyc,,
imap_tools/__pycache__/utils.cpython-312.pyc,,
imap_tools/consts.py,sha256=nWJSMxTBK8CulzikgyYijvqvrA9ju4TqJz4xIvBPab8,2607
imap_tools/errors.py,sha256=NC4dAOjnopGKYivO6bD6QpA0aXL2Bgx0whJP2k9bomA,2152
imap_tools/folder.py,sha256=aG5uQY3BezdCu2nty7Sj2l1x3en2ZcGVCvLolQy3RtQ,7101
imap_tools/idle.py,sha256=Iz92DGixdDTppdeGJvA8w8WlGgiMmdZzghYchwP6lXI,4465
imap_tools/imap_utf7.py,sha256=XbFi5jHQKozAs7278_8rvpZ5R32R3ii7g0aLlB0oWwY,2163
imap_tools/mailbox.py,sha256=0XOLgKEyIi9t4lje43v0KpS9q-iceMOCozFzQzoVRaQ,20898
imap_tools/message.py,sha256=d8jo2FSmkRS-pfy6r2NPbTnS2nlb4zkIpslurpdmeTg,11532
imap_tools/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
imap_tools/query.py,sha256=tChuvYS_q4Lk1nUbQ76-KzjInAez9ZCDOAHhR-KJgik,15975
imap_tools/utils.py,sha256=AKj9LILFqm0Hu7Hn4sgLd06asESDeT-b3BBMPScvf9c,9018
