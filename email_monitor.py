#!/usr/bin/env python3
"""
Office 365 Email Monitor for Password Reset Emails
Monitors inbox for emails containing password reset keywords and extracts email addresses.
"""

import imaplib
import email
import re
import time
import logging
from datetime import datetime
from typing import List, Dict, Optional
import ssl
import socket
import os
import sys

# Try to import configuration from config.py, fallback to default
try:
    from config import EMAIL_CONFIG as CONFIG
except ImportError:
    # Default configuration if config.py is not available
    CONFIG = {
        "username": "<EMAIL>",
        "password": "lmwwchqmkxscfjbc",
        "imap_server": "outlook.office365.com",
        "imap_port": 993,
        "polling_interval": 30,  # seconds
        "mark_as_read": False,
        "subject_keywords": ["reset", "password reset", "reset password"]
    }

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('email_monitor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EmailMonitor:
    def __init__(self, config: Dict):
        self.config = config
        self.imap_conn = None
        self.processed_emails = set()
        
    def connect_to_imap(self) -> bool:
        """Establish IMAP connection to Office 365"""
        try:
            logger.info(f"Attempting to connect to {self.config['imap_server']}:{self.config['imap_port']}")
            logger.info(f"Username: {self.config['username']}")

            # Create SSL context
            context = ssl.create_default_context()

            # Connect to IMAP server
            self.imap_conn = imaplib.IMAP4_SSL(
                self.config["imap_server"],
                self.config["imap_port"],
                ssl_context=context
            )

            logger.info("SSL connection established, attempting login...")

            # Login
            self.imap_conn.login(
                self.config["username"],
                self.config["password"]
            )

            # Select inbox
            self.imap_conn.select('INBOX')

            logger.info(f"Successfully connected to {self.config['imap_server']}")
            return True

        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP authentication error: {e}")
            logger.error("Possible causes:")
            logger.error("1. Incorrect username or password")
            logger.error("2. App password not enabled (required for Office 365 with MFA)")
            logger.error("3. Account locked or disabled")
            logger.error("4. IMAP not enabled for this account")
            return False
        except socket.gaierror as e:
            logger.error(f"Network connectivity error: {e}")
            logger.error("Check your internet connection and firewall settings")
            return False
        except Exception as e:
            logger.error(f"Unexpected error connecting to IMAP: {e}")
            return False
    
    def disconnect_from_imap(self):
        """Close IMAP connection"""
        if self.imap_conn:
            try:
                self.imap_conn.close()
                self.imap_conn.logout()
                logger.info("Disconnected from IMAP server")
            except Exception as e:
                logger.error(f"Error disconnecting from IMAP: {e}")
    
    def extract_email_addresses(self, text: str) -> List[str]:
        """Extract email addresses from text using regex"""
        if not text:
            return []
        
        # Comprehensive email regex pattern
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        
        # Find all email addresses
        emails = re.findall(email_pattern, text)
        
        # Remove duplicates and return
        return list(set(emails))
    
    def get_email_body(self, msg) -> Dict[str, str]:
        """Extract email body content (both plain text and HTML)"""
        body_content = {"plain": "", "html": ""}
        
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    if content_type == "text/plain":
                        body_content["plain"] = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                    elif content_type == "text/html":
                        body_content["html"] = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                # Single part message
                content_type = msg.get_content_type()
                if content_type == "text/plain":
                    body_content["plain"] = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
                elif content_type == "text/html":
                    body_content["html"] = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        
        except Exception as e:
            logger.error(f"Error extracting email body: {e}")
        
        return body_content
    
    def subject_matches_keywords(self, subject: str) -> bool:
        """Check if subject contains any of the specified keywords"""
        if not subject:
            return False
        
        subject_lower = subject.lower()
        return any(keyword.lower() in subject_lower for keyword in self.config["subject_keywords"])
    
    def process_email(self, email_id: str, msg) -> Optional[Dict]:
        """Process a single email and extract relevant information"""
        try:
            # Get email metadata
            subject = msg.get("Subject", "")
            sender = msg.get("From", "")
            date_received = msg.get("Date", "")
            
            # Check if subject matches keywords
            if not self.subject_matches_keywords(subject):
                return None
            
            # Get email body
            body_content = self.get_email_body(msg)
            
            # Extract email addresses from both plain text and HTML
            all_text = body_content["plain"] + " " + body_content["html"]
            found_emails = self.extract_email_addresses(all_text)
            
            # Create result dictionary
            result = {
                "email_id": email_id,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "date_received": date_received,
                "subject": subject,
                "sender": sender,
                "found_emails": found_emails,
                "body_plain": body_content["plain"][:500] + "..." if len(body_content["plain"]) > 500 else body_content["plain"]
            }
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing email {email_id}: {e}")
            return None
    
    def display_email_info(self, email_info: Dict):
        """Display email information in formatted console output"""
        print("\n" + "="*80)
        print("🔍 PASSWORD RESET EMAIL DETECTED")
        print("="*80)
        print(f"📅 Timestamp: {email_info['timestamp']}")
        print(f"📧 Subject: {email_info['subject']}")
        print(f"👤 Sender: {email_info['sender']}")
        print(f"📨 Date Received: {email_info['date_received']}")
        
        if email_info['found_emails']:
            print(f"\n📋 Found Email Addresses ({len(email_info['found_emails'])}):")
            for i, email_addr in enumerate(email_info['found_emails'], 1):
                print(f"   {i}. {email_addr}")
        else:
            print("\n❌ No email addresses found in message body")
        
        print(f"\n📄 Message Preview:")
        print(f"   {email_info['body_plain'][:200]}...")
        print("="*80)
    
    def mark_email_as_read(self, email_id: str):
        """Mark email as read if configured to do so"""
        if self.config["mark_as_read"]:
            try:
                self.imap_conn.store(email_id, '+FLAGS', '\\Seen')
                logger.debug(f"Marked email {email_id} as read")
            except Exception as e:
                logger.error(f"Error marking email {email_id} as read: {e}")
    
    def check_for_new_emails(self):
        """Check for new emails and process matching ones"""
        try:
            # Search for unseen emails
            status, messages = self.imap_conn.search(None, 'UNSEEN')
            
            if status != 'OK':
                logger.error("Failed to search for emails")
                return
            
            email_ids = messages[0].split()
            
            if not email_ids:
                logger.debug("No new emails found")
                return
            
            logger.info(f"Found {len(email_ids)} new emails to check")
            
            for email_id in email_ids:
                email_id_str = email_id.decode()
                
                # Skip if already processed
                if email_id_str in self.processed_emails:
                    continue
                
                # Fetch email
                status, msg_data = self.imap_conn.fetch(email_id, '(RFC822)')
                
                if status != 'OK':
                    logger.error(f"Failed to fetch email {email_id_str}")
                    continue
                
                # Parse email
                raw_email = msg_data[0][1]
                msg = email.message_from_bytes(raw_email)
                
                # Process email
                email_info = self.process_email(email_id_str, msg)
                
                if email_info:
                    self.display_email_info(email_info)
                    self.mark_email_as_read(email_id_str)
                    logger.info(f"Processed password reset email from {email_info['sender']}")
                
                # Mark as processed
                self.processed_emails.add(email_id_str)
                
        except Exception as e:
            logger.error(f"Error checking for new emails: {e}")
    
    def run_monitor(self):
        """Main monitoring loop"""
        logger.info("Starting Office 365 Email Monitor...")
        logger.info(f"Monitoring account: {self.config['username']}")
        logger.info(f"Keywords: {', '.join(self.config['subject_keywords'])}")
        logger.info(f"Polling interval: {self.config['polling_interval']} seconds")
        
        while True:
            try:
                # Connect if not connected
                if not self.imap_conn:
                    if not self.connect_to_imap():
                        logger.error("Failed to connect. Retrying in 60 seconds...")
                        time.sleep(60)
                        continue
                
                # Check for new emails
                self.check_for_new_emails()
                
                # Wait before next check
                time.sleep(self.config["polling_interval"])
                
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error in monitoring loop: {e}")
                # Disconnect and reconnect on error
                self.disconnect_from_imap()
                self.imap_conn = None
                time.sleep(30)
        
        # Cleanup
        self.disconnect_from_imap()

def main():
    """Main function"""
    print("🚀 Office 365 Email Monitor Starting...")
    print(f"📧 Monitoring: {CONFIG['username']}")
    print("🔍 Looking for password reset emails...")
    print("⏹️  Press Ctrl+C to stop\n")
    
    monitor = EmailMonitor(CONFIG)
    monitor.run_monitor()

if __name__ == "__main__":
    main()
