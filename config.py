"""
Configuration file for Office 365 Email Monitor
Modify these settings as needed for your environment
"""

# Email Account Configuration
EMAIL_CONFIG = {
    # Office 365 credentials
    "username": "<EMAIL>",
    "password": "lmwwchqmkxscfjbc",  # App password for Office 365
    
    # IMAP server settings for Office 365
    "imap_server": "outlook.office365.com",
    "imap_port": 993,
    
    # Monitoring settings
    "polling_interval": 30,  # Check for new emails every 30 seconds
    "mark_as_read": False,   # Set to True to mark processed emails as read
    
    # Subject line keywords to filter (case-insensitive)
    "subject_keywords": [
        "reset",
        "password reset", 
        "reset password",
        "forgot password",
        "account recovery",
        "password recovery"
    ]
}

# Logging Configuration
LOGGING_CONFIG = {
    "log_level": "INFO",  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    "log_file": "email_monitor.log",
    "console_output": True
}

# Display Configuration
DISPLAY_CONFIG = {
    "show_email_preview": True,
    "preview_length": 200,  # Characters to show in email preview
    "show_timestamps": True,
    "show_email_count": True
}

# Security Settings
SECURITY_CONFIG = {
    "ssl_verify": True,
    "connection_timeout": 30,  # seconds
    "max_retry_attempts": 3,
    "retry_delay": 60  # seconds between retry attempts
}
