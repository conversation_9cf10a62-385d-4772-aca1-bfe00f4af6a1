# Office 365 Email Monitor PowerShell Launcher
Write-Host "🚀 Starting Office 365 Email Monitor..." -ForegroundColor Green
Write-Host ""
Write-Host "📧 Monitoring: <EMAIL>" -ForegroundColor Cyan
Write-Host "🔍 Looking for password reset emails..." -ForegroundColor Yellow
Write-Host "⏹️  Press Ctrl+C to stop" -ForegroundColor Red
Write-Host ""

try {
    python email_monitor.py
}
catch {
    Write-Host "❌ Error running email monitor: $_" -ForegroundColor Red
    Write-Host "Make sure Python is installed and in your PATH" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Press any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
