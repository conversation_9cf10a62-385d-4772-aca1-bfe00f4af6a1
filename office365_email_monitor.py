#!/usr/bin/env python3
"""
Simple Office 365 Email Monitor
Monitors inbox for password reset emails and extracts email addresses
"""

import imaplib
import email
import re
import time
import logging
from datetime import datetime
import ssl
import socket

# Configuration - Update these settings as needed
CONFIG = {
    "username": "<EMAIL>",
    "password": "gmlwghpjyfhkkqlv",  # App password
    "imap_server": "outlook.office365.com",
    "imap_port": 993,
    "polling_interval": 30,  # seconds between checks
    "mark_as_read": False,
    "subject_keywords": ["reset", "password reset", "reset password", "forgot password", "account recovery"]
}

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EmailMonitor:
    def __init__(self):
        self.imap_conn = None
        self.processed_emails = set()
    
    def connect_to_imap(self):
        """Connect to Office 365 IMAP with enhanced authentication"""
        try:
            logger.info(f"Connecting to {CONFIG['imap_server']}...")
            
            # Create SSL context with specific settings for Office 365
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            
            # Connect with timeout
            self.imap_conn = imaplib.IMAP4_SSL(
                CONFIG["imap_server"], 
                CONFIG["imap_port"],
                ssl_context=context
            )
            
            # Set timeout for operations
            self.imap_conn.sock.settimeout(30)
            
            logger.info("Attempting authentication...")
            
            # Try authentication with different methods
            try:
                # Method 1: Standard login
                self.imap_conn.login(CONFIG["username"], CONFIG["password"])
            except imaplib.IMAP4.error as e:
                logger.warning(f"Standard login failed: {e}")
                
                # Method 2: Try with AUTHENTICATE PLAIN
                try:
                    import base64
                    auth_string = f'\0{CONFIG["username"]}\0{CONFIG["password"]}'
                    auth_string = base64.b64encode(auth_string.encode()).decode()
                    self.imap_conn.authenticate('PLAIN', lambda x: auth_string)
                except Exception as e2:
                    logger.error(f"PLAIN authentication also failed: {e2}")
                    raise e
            
            # Select inbox
            status, messages = self.imap_conn.select('INBOX')
            if status != 'OK':
                raise Exception(f"Failed to select INBOX: {messages}")
            
            logger.info("Successfully connected and authenticated!")
            return True
            
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            if "LOGIN failed" in str(e):
                logger.error("Authentication troubleshooting:")
                logger.error("1. Verify the app password is correct")
                logger.error("2. Check if IMAP is enabled in Outlook settings")
                logger.error("3. Ensure the account doesn't have conditional access policies blocking IMAP")
                logger.error("4. Try generating a new app password")
            return False
    
    def extract_email_addresses(self, text):
        """Extract email addresses from text"""
        if not text:
            return []
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return list(set(re.findall(email_pattern, text)))
    
    def get_email_body(self, msg):
        """Extract email body content"""
        body_text = ""
        try:
            if msg.is_multipart():
                for part in msg.walk():
                    if part.get_content_type() in ["text/plain", "text/html"]:
                        if "attachment" not in str(part.get("Content-Disposition", "")):
                            body_text += part.get_payload(decode=True).decode('utf-8', errors='ignore') + " "
            else:
                if msg.get_content_type() in ["text/plain", "text/html"]:
                    body_text = msg.get_payload(decode=True).decode('utf-8', errors='ignore')
        except Exception as e:
            logger.error(f"Error extracting email body: {e}")
        return body_text
    
    def subject_matches_keywords(self, subject):
        """Check if subject contains password reset keywords"""
        if not subject:
            return False
        subject_lower = subject.lower()
        return any(keyword.lower() in subject_lower for keyword in CONFIG["subject_keywords"])
    
    def display_email_info(self, email_info):
        """Display formatted email information"""
        print("\n" + "="*60)
        print("🔍 PASSWORD RESET EMAIL DETECTED")
        print("="*60)
        print(f"📅 Timestamp: {email_info['timestamp']}")
        print(f"📧 Subject: {email_info['subject']}")
        print(f"👤 Sender: {email_info['sender']}")
        
        if email_info['found_emails']:
            print(f"\n📋 Found Email Addresses ({len(email_info['found_emails'])}):")
            for i, email_addr in enumerate(email_info['found_emails'], 1):
                print(f"   {i}. {email_addr}")
        else:
            print("\n❌ No email addresses found in message body")
        
        print("="*60)
    
    def check_for_new_emails(self):
        """Check for new emails and process matching ones"""
        try:
            # Search for unread emails
            status, messages = self.imap_conn.search(None, 'UNSEEN')
            if status != 'OK':
                logger.error("Failed to search for emails")
                return
            
            email_ids = messages[0].split()
            if not email_ids:
                logger.debug("No new emails found")
                return
            
            logger.info(f"Checking {len(email_ids)} new emails...")
            
            for email_id in email_ids:
                email_id_str = email_id.decode()
                
                if email_id_str in self.processed_emails:
                    continue
                
                # Fetch email
                status, msg_data = self.imap_conn.fetch(email_id, '(RFC822)')
                if status != 'OK':
                    continue
                
                # Parse email
                raw_email = msg_data[0][1]
                msg = email.message_from_bytes(raw_email)
                
                # Check subject
                subject = msg.get("Subject", "")
                if not self.subject_matches_keywords(subject):
                    self.processed_emails.add(email_id_str)
                    continue
                
                # Extract information
                sender = msg.get("From", "")
                body_text = self.get_email_body(msg)
                found_emails = self.extract_email_addresses(body_text)
                
                # Display results
                email_info = {
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "subject": subject,
                    "sender": sender,
                    "found_emails": found_emails
                }
                
                self.display_email_info(email_info)
                
                # Mark as read if configured
                if CONFIG["mark_as_read"]:
                    self.imap_conn.store(email_id, '+FLAGS', '\\Seen')
                
                self.processed_emails.add(email_id_str)
                logger.info(f"Processed email from {sender}")
                
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
            # Reset connection on error
            self.imap_conn = None
    
    def run_monitor(self):
        """Main monitoring loop"""
        print("🚀 Office 365 Email Monitor Starting...")
        print(f"📧 Monitoring: {CONFIG['username']}")
        print(f"🔍 Keywords: {', '.join(CONFIG['subject_keywords'])}")
        print("⏹️  Press Ctrl+C to stop\n")
        
        while True:
            try:
                # Connect if needed
                if not self.imap_conn:
                    if not self.connect_to_imap():
                        logger.error("Connection failed. Retrying in 60 seconds...")
                        time.sleep(60)
                        continue
                
                # Check for emails
                self.check_for_new_emails()
                
                # Wait before next check
                time.sleep(CONFIG["polling_interval"])
                
            except KeyboardInterrupt:
                print("\n👋 Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Unexpected error: {e}")
                self.imap_conn = None
                time.sleep(30)
        
        # Cleanup
        if self.imap_conn:
            try:
                self.imap_conn.close()
                self.imap_conn.logout()
            except:
                pass

def test_connection():
    """Test the IMAP connection"""
    print("🔧 Testing Office 365 connection...")
    monitor = EmailMonitor()
    
    if monitor.connect_to_imap():
        print("✅ Connection test successful!")
        monitor.imap_conn.close()
        monitor.imap_conn.logout()
        return True
    else:
        print("❌ Connection test failed!")
        return False

def main():
    """Main function"""
    print("Office 365 Email Monitor")
    print("=" * 40)
    
    # Test connection first
    if not test_connection():
        print("\n⚠️ Please fix the connection issue before running the monitor.")
        input("Press Enter to exit...")
        return
    
    # Start monitoring
    monitor = EmailMonitor()
    monitor.run_monitor()

if __name__ == "__main__":
    main()
