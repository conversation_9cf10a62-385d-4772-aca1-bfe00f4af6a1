import re
import time
import logging
from datetime import datetime
from imap_tools import MailBox, MailMessageFlags, MailboxLoginError
import socket
import os

# ========================== CONFIGURATION ==========================

# --- Credentials ---
# !! IMPORTANT SECURITY WARNING !!
# Avoid storing credentials directly in code. Use environment variables
# or a secure vault for production use.
# To use environment variables:
# 1. Create a file named .env in the same directory with:
#    O365_USER="<EMAIL>"
#    O365_PASS="gmlwghpjyfhkkqlv"
# 2. Install the library: pip install python-dotenv
# 3. Uncomment the following lines:
# from dotenv import load_dotenv
# load_dotenv()
# IMAP_USER = os.getenv("O365_USER")
# IMAP_PASSWORD = os.getenv("O365_PASS")

IMAP_SERVER = "outlook.office365.com"
IMAP_USER = "<EMAIL>"
IMAP_PASSWORD = "lmwwchqmkxscfjbc"  # This is likely an App Password

# --- Settings ---
# Polling interval in seconds to check for new emails.
POLLING_INTERVAL = 60

# Keywords to filter email subjects (case-insensitive).
SUBJECT_KEYWORDS = ["reset", "password reset", "reset password"]

# Set to True to mark processed emails as read, False to leave them unread.
MARK_AS_READ = True

# ===================================================================

# Setup logging for clear console output
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def find_emails_in_body(body_text):
    """
    Uses regex to find all unique email addresses in a given string.
    
    Args:
        body_text (str): The combined text/HTML content of the email.
        
    Returns:
        list: A list of unique email addresses found.
    """
    if not body_text:
        return []
    # A robust regex for finding email addresses
    email_regex = r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}'
    found_emails = re.findall(email_regex, body_text)
    # Return a list of unique emails to avoid duplicates
    return list(set(found_emails))

def process_matching_email(msg):
    """
    Processes a single email message that matches the filter criteria,
    extracts details, and prints them.
    
    Args:
        msg (imap_tools.MailMessage): The email message object.
    """
    try:
        # Combine plain text and HTML bodies for a comprehensive search
        full_body = (msg.text or "") + "\n" + (msg.html or "")
        
        # Find all email addresses in the body
        body_emails = find_emails_in_body(full_body)
        
        # Format and print the output
        print("\n" + "="*25 + " MATCHING EMAIL FOUND " + "="*25)
        print(f"  Timestamp:      {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"  Subject:        {msg.subject}")
        print(f"  From:           {msg.from_}")
        print(f"  Emails in Body: {', '.join(body_emails) if body_emails else 'None found'}")
        print("="*72 + "\n")
        
    except Exception as e:
        logging.error(f"Failed to process email UID {msg.uid}: {e}")

def monitor_inbox():
    """
    Main function to continuously monitor the Office 365 inbox.
    """
    logging.info("Starting email monitor script...")
    if IMAP_PASSWORD == "lmwwchqmkxscfjbc":
        logging.warning("Using hardcoded credentials. This is not recommended for production.")
    
    while True:
        try:
            # Use 'with' statement for automatic login and logout
            with MailBox(IMAP_SERVER).login(IMAP_USER, IMAP_PASSWORD, 'INBOX') as mailbox:
                logging.info(f"Successfully connected as {IMAP_USER}. Checking for new mail...")
                
                # Fetch unseen emails without marking them as read automatically
                unseen_msgs = mailbox.fetch(criteria='UNSEEN', mark_seen=False)
                
                found_match = False
                for msg in unseen_msgs:
                    subject_lower = msg.subject.lower()
                    
                    # Check if any keyword is in the subject
                    if any(keyword in subject_lower for keyword in SUBJECT_KEYWORDS):
                        found_match = True
                        process_matching_email(msg)
                        
                        # Mark the email as read if configured to do so
                        if MARK_AS_READ:
                            mailbox.flag(msg.uid, MailMessageFlags.SEEN, True)
                            logging.info(f"Marked email (UID: {msg.uid}, Subject: '{msg.subject}') as read.")
                
                if not found_match:
                    logging.info("No new matching emails found.")

        except (socket.gaierror, ConnectionRefusedError, MailboxLoginError) as e:
            logging.error(f"Authentication or Connection Error: {e}. Retrying in {POLLING_INTERVAL} seconds...")
        except Exception as e:
            logging.error(f"An unexpected error occurred: {e}. Retrying in {POLLING_INTERVAL} seconds...")
            
        # Wait before the next check
        logging.info(f"Waiting for {POLLING_INTERVAL} seconds before next check...")
        time.sleep(POLLING_INTERVAL)

if __name__ == "__main__":
    try:
        monitor_inbox()
    except KeyboardInterrupt:
        logging.info("\nScript stopped by user. Exiting gracefully.")