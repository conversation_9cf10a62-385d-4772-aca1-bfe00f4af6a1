# Office 365 Email Monitor

A Python script that monitors an Office 365 email account in real-time for incoming emails containing password reset keywords and extracts email addresses from the message body.

## Features

- **Real-time monitoring** of Office 365 inbox using IMAP
- **Subject filtering** for password reset related emails
- **Email address extraction** from message body using regex
- **Comprehensive error handling** for network and authentication issues
- **Configurable settings** for polling interval and keywords
- **Detailed logging** to both console and file
- **Clean console output** with formatted email information

## Requirements

- Python 3.6 or higher
- Office 365 account with app password enabled
- Network access to outlook.office365.com

## Installation

1. Clone or download the script files
2. No additional packages required - uses only Python standard library

## Configuration

### Email Credentials
Update the credentials in `email_monitor.py` or `config.py`:
```python
"username": "<EMAIL>",
"password": "your-app-password",
```

### App Password Setup
For Office 365 accounts with MFA enabled:
1. Go to Microsoft Account Security settings
2. Enable "App passwords"
3. Generate a new app password
4. Use this app password instead of your regular password

### Customizable Settings
- **Polling interval**: How often to check for new emails (default: 30 seconds)
- **Subject keywords**: Keywords to filter emails (case-insensitive)
- **Mark as read**: Whether to mark processed emails as read
- **Logging level**: Control verbosity of logs

## Usage

### Basic Usage
```bash
python email_monitor.py
```

### Running in Background
```bash
# Windows
python email_monitor.py > output.log 2>&1

# Linux/Mac
nohup python email_monitor.py > output.log 2>&1 &
```

## Output Format

When a matching email is found, the script displays:
```
================================================================================
🔍 PASSWORD RESET EMAIL DETECTED
================================================================================
📅 Timestamp: 2024-01-15 14:30:25
📧 Subject: Password Reset Request
👤 Sender: <EMAIL>
📨 Date Received: Mon, 15 Jan 2024 14:30:20 +0000

📋 Found Email Addresses (2):
   1. <EMAIL>
   2. <EMAIL>

📄 Message Preview:
   Dear user, please click the following link to reset your password...
================================================================================
```

## Monitored Keywords

The script filters emails containing these keywords in the subject line:
- "reset"
- "password reset"
- "reset password"
- "forgot password"
- "account recovery"
- "password recovery"

## Error Handling

The script handles:
- **Authentication failures**: Logs error and retries connection
- **Network connectivity issues**: Automatic reconnection with backoff
- **Email parsing errors**: Continues processing other emails
- **IMAP connection drops**: Automatic reconnection

## Logging

Logs are written to:
- **Console**: Real-time status and email notifications
- **File**: `email_monitor.log` with detailed information

## Security Considerations

- Use app passwords instead of regular passwords
- Store credentials securely
- Consider using environment variables for sensitive data
- Monitor log files for unauthorized access attempts

## Troubleshooting

### Common Issues

1. **Authentication Error**
   - Verify username and app password
   - Ensure MFA is enabled and app password is generated
   - Check if account is locked

2. **Connection Timeout**
   - Verify network connectivity
   - Check firewall settings for port 993
   - Ensure outlook.office365.com is accessible

3. **No Emails Detected**
   - Check subject keywords configuration
   - Verify emails are arriving in inbox (not spam)
   - Check if emails are already marked as read

### Debug Mode
Enable debug logging by changing log level in config:
```python
"log_level": "DEBUG"
```

## Customization

### Adding New Keywords
Edit the `subject_keywords` list in the configuration:
```python
"subject_keywords": [
    "reset",
    "password reset",
    "your-custom-keyword"
]
```

### Changing Email Regex
Modify the `extract_email_addresses` method to use different patterns:
```python
email_pattern = r'your-custom-regex-pattern'
```

## License

This script is provided as-is for educational and monitoring purposes.

## Support

For issues or questions:
1. Check the log files for error details
2. Verify configuration settings
3. Test network connectivity to Office 365 servers
