#!/usr/bin/env python3
"""
Connection test script for Office 365 IMAP
Tests the connection without running the full monitor
"""

import imaplib
import ssl
import socket
import sys

def test_office365_connection():
    """Test connection to Office 365 IMAP server"""
    
    # Configuration
    username = "<EMAIL>"
    password = "lmwwchqmkxscfjbc"
    imap_server = "outlook.office365.com"
    imap_port = 993
    
    print("🔧 Office 365 IMAP Connection Test")
    print("=" * 50)
    print(f"Server: {imap_server}:{imap_port}")
    print(f"Username: {username}")
    print(f"Password: {'*' * len(password)}")
    print()
    
    try:
        print("1️⃣ Testing network connectivity...")
        
        # Test basic connectivity
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((imap_server, imap_port))
        sock.close()
        
        if result != 0:
            print("❌ Cannot connect to server. Check your internet connection.")
            return False
        
        print("✅ Network connectivity OK")
        
        print("\n2️⃣ Testing SSL connection...")
        
        # Create SSL context
        context = ssl.create_default_context()
        
        # Connect to IMAP server
        imap_conn = imaplib.IMAP4_SSL(
            imap_server, 
            imap_port,
            ssl_context=context
        )
        
        print("✅ SSL connection established")
        
        print("\n3️⃣ Testing authentication...")
        
        # Attempt login
        imap_conn.login(username, password)
        
        print("✅ Authentication successful")
        
        print("\n4️⃣ Testing mailbox access...")
        
        # Select inbox
        status, messages = imap_conn.select('INBOX')
        
        if status == 'OK':
            message_count = int(messages[0])
            print(f"✅ Mailbox access OK - {message_count} messages in inbox")
        else:
            print("⚠️ Warning: Could not access inbox")
        
        print("\n5️⃣ Testing email search...")
        
        # Test search functionality
        status, messages = imap_conn.search(None, 'ALL')
        
        if status == 'OK':
            email_ids = messages[0].split()
            print(f"✅ Email search OK - Found {len(email_ids)} emails")
        else:
            print("⚠️ Warning: Could not search emails")
        
        # Cleanup
        imap_conn.close()
        imap_conn.logout()
        
        print("\n🎉 All tests passed! The email monitor should work correctly.")
        return True
        
    except imaplib.IMAP4.error as e:
        print(f"❌ IMAP authentication error: {e}")
        print("\n🔧 Troubleshooting steps:")
        print("1. Verify your username and password are correct")
        print("2. Enable 'App passwords' in your Microsoft account security settings")
        print("3. Generate a new app password and use it instead of your regular password")
        print("4. Ensure IMAP is enabled in your Outlook settings")
        print("5. Check if your account has multi-factor authentication enabled")
        return False
        
    except socket.gaierror as e:
        print(f"❌ Network error: {e}")
        print("\n🔧 Troubleshooting steps:")
        print("1. Check your internet connection")
        print("2. Verify DNS resolution for outlook.office365.com")
        print("3. Check firewall settings for port 993")
        print("4. Try connecting from a different network")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("\n🔧 Please check your configuration and try again")
        return False

def main():
    """Main function"""
    success = test_office365_connection()
    
    if success:
        print("\n💡 Ready to run the email monitor:")
        print("   python email_monitor.py")
    else:
        print("\n⚠️ Please resolve the connection issues before running the monitor.")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
