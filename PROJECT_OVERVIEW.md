# Office 365 Email Monitor - Project Overview

## 📁 Project Structure

```
📦 Office 365 Email Monitor
├── 📄 email_monitor.py          # Main monitoring script
├── 📄 config.py                 # Configuration settings
├── 📄 test_connection.py        # Connection testing utility
├── 📄 test_email_extraction.py  # Email parsing tests
├── 📄 requirements.txt          # Dependencies (none required)
├── 📄 README.md                 # User documentation
├── 📄 TROUBLESHOOTING.md        # Troubleshooting guide
├── 📄 PROJECT_OVERVIEW.md       # This file
├── 🔧 run_monitor.bat           # Windows batch launcher
├── 🔧 run_monitor.ps1           # PowerShell launcher
└── 📄 email_monitor.log         # Generated log file
```

## 🚀 Quick Start

### 1. Test Connection First
```bash
python test_connection.py
```

### 2. Run Email Extraction Tests
```bash
python test_email_extraction.py
```

### 3. Start Monitoring
```bash
python email_monitor.py
```

**Or use the launchers:**
- Windows: Double-click `run_monitor.bat`
- PowerShell: `.\run_monitor.ps1`

## ⚙️ Configuration

### Current Settings (config.py)
- **Username:** <EMAIL>
- **Password:** lmwwchqmkxscfjbc (App password required)
- **Server:** outlook.office365.com:993
- **Polling:** Every 30 seconds
- **Keywords:** reset, password reset, reset password, forgot password, account recovery, password recovery

### Key Features Implemented

✅ **Real-time Email Monitoring**
- IMAP connection to Office 365
- Continuous polling for new emails
- Automatic reconnection on failures

✅ **Subject Line Filtering**
- Case-insensitive keyword matching
- Configurable keyword list
- Multiple keyword support

✅ **Email Address Extraction**
- Regex-based email parsing
- Supports both plain text and HTML
- Handles multiple email formats

✅ **Comprehensive Error Handling**
- Authentication failure recovery
- Network connectivity issues
- Email parsing errors
- Graceful degradation

✅ **Detailed Logging**
- Console and file logging
- Configurable log levels
- Timestamp tracking

✅ **Formatted Output**
- Clean console display
- Email metadata extraction
- Found email addresses listing

## 🔧 Authentication Setup Required

The script is ready to run but requires proper authentication setup:

### For Office 365 with MFA (Recommended):
1. Go to https://account.microsoft.com/security
2. Enable "App passwords"
3. Generate new app password
4. Replace the password in config.py

### For Basic Authentication:
- Ensure IMAP is enabled in Outlook settings
- Use regular account password

## 📊 Test Results

All tests passed successfully:
- ✅ Email address extraction (6/6 tests)
- ✅ Subject line filtering (11/11 tests)
- ✅ Network connectivity
- ✅ SSL connection
- ❌ Authentication (requires app password setup)

## 🎯 Usage Scenarios

### 1. Security Monitoring
Monitor for password reset attempts across your organization

### 2. Phishing Detection
Identify suspicious password reset emails

### 3. Account Recovery Tracking
Track legitimate password reset requests

### 4. Email Forensics
Extract email addresses from security-related messages

## 🛡️ Security Considerations

- Uses secure IMAP over SSL (port 993)
- App passwords recommended over regular passwords
- Logs contain sensitive information - secure appropriately
- No email content is stored permanently

## 📈 Performance

- **Memory Usage:** Minimal (standard Python libraries only)
- **CPU Usage:** Low (polling-based, not real-time push)
- **Network Usage:** Minimal (IMAP commands only)
- **Scalability:** Single account monitoring

## 🔄 Monitoring Workflow

1. **Connect** to Office 365 IMAP server
2. **Search** for unread emails in INBOX
3. **Filter** by subject line keywords
4. **Extract** email addresses from message body
5. **Display** results in formatted output
6. **Log** all activities
7. **Wait** for polling interval
8. **Repeat** until stopped

## 📝 Output Example

```
================================================================================
🔍 PASSWORD RESET EMAIL DETECTED
================================================================================
📅 Timestamp: 2024-01-15 14:30:25
📧 Subject: Password Reset Request
👤 Sender: <EMAIL>
📨 Date Received: Mon, 15 Jan 2024 14:30:20 +0000

📋 Found Email Addresses (2):
   1. <EMAIL>
   2. <EMAIL>

📄 Message Preview:
   Dear user, please click the following link to reset your password...
================================================================================
```

## 🚨 Known Limitations

1. **IMAP Only:** Requires IMAP access (not available in all Office 365 plans)
2. **Polling-Based:** Not real-time (30-second intervals)
3. **Single Account:** Monitors one email account at a time
4. **Inbox Only:** Only monitors the main INBOX folder
5. **Unread Emails:** Only processes unread emails by default

## 🔮 Future Enhancements

- OAuth2 authentication support
- Multiple account monitoring
- Real-time push notifications
- Web dashboard interface
- Email attachment analysis
- Advanced filtering rules
- Database storage of results

## 📞 Support

- Check `TROUBLESHOOTING.md` for common issues
- Review `email_monitor.log` for detailed error information
- Test connection with `test_connection.py`
- Verify email parsing with `test_email_extraction.py`
