#!/usr/bin/env python3
"""
Test script for email address extraction functionality
"""

import re
from email_monitor import EmailMonitor

def test_email_extraction():
    """Test the email extraction functionality"""
    
    # Create monitor instance
    config = {"subject_keywords": ["reset", "password"]}
    monitor = EmailMonitor(config)
    
    # Test cases
    test_cases = [
        {
            "name": "Simple email in text",
            "text": "<NAME_EMAIL> for assistance.",
            "expected": ["<EMAIL>"]
        },
        {
            "name": "Multiple emails",
            "text": "Contact admin@company.<NAME_EMAIL> for help.",
            "expected": ["<EMAIL>", "<EMAIL>"]
        },
        {
            "name": "Email in HTML",
            "text": '<a href="mailto:<EMAIL>">Click here</a>',
            "expected": ["<EMAIL>"]
        },
        {
            "name": "Mixed content",
            "text": "Reset your password <NAME_EMAIL> or visiting https://example.com. Contact <EMAIL>",
            "expected": ["<EMAIL>", "<EMAIL>"]
        },
        {
            "name": "No emails",
            "text": "This text contains no email addresses.",
            "expected": []
        },
        {
            "name": "Invalid email formats",
            "text": "Invalid: @domain.com, user@, incomplete@domain",
            "expected": []
        }
    ]
    
    print("🧪 Testing Email Address Extraction")
    print("=" * 50)
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest {i}: {test_case['name']}")
        print(f"Input: {test_case['text'][:50]}...")
        
        # Extract emails
        found_emails = monitor.extract_email_addresses(test_case['text'])
        
        # Sort for comparison
        found_emails.sort()
        expected = sorted(test_case['expected'])
        
        # Check result
        if found_emails == expected:
            print(f"✅ PASSED - Found: {found_emails}")
        else:
            print(f"❌ FAILED - Expected: {expected}, Found: {found_emails}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All tests passed!")
    else:
        print("⚠️  Some tests failed!")
    
    return all_passed

def test_subject_filtering():
    """Test subject line filtering"""
    
    config = {"subject_keywords": ["reset", "password reset", "recovery"]}
    monitor = EmailMonitor(config)
    
    test_subjects = [
        ("Password Reset Request", True),
        ("Your account needs a reset", True),
        ("Account Recovery Instructions", True),
        ("Password reset for your account", True),
        ("RESET YOUR PASSWORD NOW", True),
        ("Welcome to our service", False),
        ("Invoice for your order", False),
        ("Meeting reminder", False),
        ("", False),
        ("Reset", True),
        ("password", False),  # Should be False as it doesn't contain "reset" or "recovery"
    ]
    
    print("\n🔍 Testing Subject Line Filtering")
    print("=" * 50)
    
    all_passed = True
    
    for subject, expected in test_subjects:
        result = monitor.subject_matches_keywords(subject)
        status = "✅ PASSED" if result == expected else "❌ FAILED"
        print(f"{status} - '{subject}' -> {result} (expected {expected})")
        
        if result != expected:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All subject filtering tests passed!")
    else:
        print("⚠️  Some subject filtering tests failed!")
    
    return all_passed

def main():
    """Run all tests"""
    print("🚀 Running Email Monitor Tests\n")
    
    test1_passed = test_email_extraction()
    test2_passed = test_subject_filtering()
    
    print("\n" + "=" * 60)
    print("📊 FINAL RESULTS")
    print("=" * 60)
    
    if test1_passed and test2_passed:
        print("🎉 ALL TESTS PASSED! The email monitor is ready to use.")
    else:
        print("⚠️  SOME TESTS FAILED! Please review the implementation.")
    
    print("\n💡 To run the actual email monitor:")
    print("   python email_monitor.py")

if __name__ == "__main__":
    main()
