# Troubleshooting Guide - Office 365 Email Monitor

## Authentication Issues

### Problem: "LOGIN failed" Error

**Symptoms:**
- Connection test shows "IMAP authentication error: <PERSON>OGI<PERSON> failed"
- Email monitor cannot connect to Office 365

**Solutions:**

#### 1. Enable App Passwords (Most Common Solution)

For Office 365 accounts with Multi-Factor Authentication (MFA):

1. **Go to Microsoft Account Security Settings:**
   - Visit: https://account.microsoft.com/security
   - Sign in with your Office 365 account

2. **Enable App Passwords:**
   - Click on "Advanced security options"
   - Find "App passwords" section
   - Click "Create a new app password"

3. **Generate App Password:**
   - Enter a name like "Email Monitor"
   - Copy the generated password (16 characters)
   - Use this password instead of your regular password

4. **Update Configuration:**
   ```python
   "password": "your-16-character-app-password"
   ```

#### 2. Enable IMAP Access

1. **Sign in to Outlook Web App:**
   - Go to https://outlook.office365.com
   - Sign in with your account

2. **Access Settings:**
   - Click the gear icon (Settings)
   - Go to "View all Outlook settings"

3. **Enable IMAP:**
   - Navigate to "Mail" > "Sync email"
   - Check "Let devices and apps use IMAP"
   - Save changes

#### 3. Check Account Type

**For Personal Microsoft Accounts:**
- Use: https://account.live.com/proofs/manage
- Enable "App passwords"

**For Work/School Accounts:**
- Contact your IT administrator
- They may need to enable IMAP access
- Modern Authentication might be required

## Network Issues

### Problem: Cannot Connect to Server

**Symptoms:**
- "Network connectivity error"
- Connection timeouts

**Solutions:**

1. **Check Internet Connection:**
   ```bash
   ping outlook.office365.com
   ```

2. **Test Port Access:**
   ```bash
   telnet outlook.office365.com 993
   ```

3. **Firewall Settings:**
   - Allow outbound connections on port 993
   - Add exception for Python.exe if needed

4. **Corporate Networks:**
   - Check if IMAP is blocked
   - Contact network administrator
   - May need to use VPN

## Configuration Issues

### Problem: No Emails Detected

**Possible Causes:**

1. **Keywords Not Matching:**
   - Check subject line keywords in config
   - Verify case-insensitive matching

2. **Emails Already Read:**
   - Script only checks unread emails by default
   - Set `mark_as_read: False` to avoid missing emails

3. **Wrong Folder:**
   - Script monitors INBOX only
   - Check if emails are in spam/junk folder

### Problem: Script Crashes

**Common Solutions:**

1. **Check Python Version:**
   ```bash
   python --version
   ```
   - Requires Python 3.6 or higher

2. **Check Dependencies:**
   - All required modules are built-in
   - No external packages needed

3. **Check File Permissions:**
   - Ensure script can write log files
   - Check directory permissions

## Testing Steps

### 1. Run Connection Test
```bash
python test_connection.py
```

### 2. Run Email Extraction Test
```bash
python test_email_extraction.py
```

### 3. Check Logs
- Review `email_monitor.log` for detailed errors
- Enable DEBUG logging for more information

## Common Error Messages

### "SSL: CERTIFICATE_VERIFY_FAILED"
**Solution:** Update Python certificates or disable SSL verification (not recommended)

### "IMAP4 instance has no attribute 'login'"
**Solution:** Check Python version and IMAP library installation

### "Connection reset by peer"
**Solution:** Network issue, try different network or VPN

## Advanced Troubleshooting

### Enable Debug Logging
```python
logging.basicConfig(level=logging.DEBUG)
```

### Test with Different Email Client
Try connecting with Thunderbird or Outlook to verify IMAP settings

### Check Office 365 Service Status
Visit: https://status.office365.com

### Alternative Authentication Methods

If app passwords don't work, consider:
1. OAuth2 authentication (requires additional setup)
2. Exchange Web Services (EWS) instead of IMAP
3. Microsoft Graph API

## Getting Help

1. **Check the logs** in `email_monitor.log`
2. **Run the connection test** to isolate the issue
3. **Verify account settings** in Outlook Web App
4. **Contact IT support** for work/school accounts

## Security Notes

- Never share your app passwords
- Use app passwords only for this specific application
- Regularly rotate app passwords
- Monitor login attempts in your Microsoft account security dashboard
